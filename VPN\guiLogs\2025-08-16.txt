2025-08-16 00:00:35.8092-DEBUG DownloadService,net_http_ssl_connection_failed
2025-08-16 00:00:35.8092-DEBUG    at System.Net.Http.ConnectHelper.EstablishSslConnectionAsync(SslClientAuthenticationOptions sslOptions, HttpRequestMessage request, Boolean async, Stream stream, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-08-16 00:00:35.8092-ERROR System.IO.IOException: net_io_readfailure, 远程主机强迫关闭了一个现有的连接。
2025-08-16 02:00:48.9530-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-16 02:00:49.2706-DEBUG ProcUtils,NoAssociatedProcess
2025-08-16 02:00:49.2706-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-16 02:00:49.5862-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-16 02:00:49.8944-DEBUG ProcUtils,NoAssociatedProcess
2025-08-16 02:00:49.8944-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-16 04:02:51.8067-INFO MyAppExitAsync Begin
2025-08-16 04:02:52.1266-INFO MyAppExitAsync End
2025-08-16 04:02:52.1317-INFO OnExit
2025-08-16 04:07:59.3729-INFO v2rayN start up | v2rayN - V7.11.2 - X64 | C:\Users\<USER>\Desktop\VPN\ | C:\Users\<USER>\Desktop\VPN\v2rayN.exe | Microsoft Windows NT 10.0.22631.0
2025-08-16 04:07:59.9500-INFO Setup Scheduled Tasks
2025-08-16 04:43:38.7637-INFO Current_SessionEnding
2025-08-16 04:43:38.7637-INFO MyAppExitAsync Begin
2025-08-16 10:53:20.3732-INFO v2rayN start up | v2rayN - V7.11.2 - X64 | C:\Users\<USER>\Desktop\VPN\ | C:\Users\<USER>\Desktop\VPN\v2rayN.exe | Microsoft Windows NT 10.0.22631.0
2025-08-16 10:53:20.8428-INFO Setup Scheduled Tasks
2025-08-16 13:23:53.5544-INFO MyAppExitAsync Begin
2025-08-16 13:23:53.8768-INFO MyAppExitAsync End
2025-08-16 13:23:53.8768-INFO OnExit
