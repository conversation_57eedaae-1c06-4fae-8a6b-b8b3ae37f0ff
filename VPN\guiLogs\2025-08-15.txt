2025-08-15 14:35:00.2138-INFO v2rayN start up | v2rayN - V7.11.2 - X64 | C:\Users\<USER>\Desktop\VPN\ | C:\Users\<USER>\Desktop\VPN\v2rayN.exe | Microsoft Windows NT 10.0.22631.0
2025-08-15 14:35:00.6601-<PERSON><PERSON><PERSON> Setup Scheduled Tasks
2025-08-15 14:56:27.0420-<PERSON><PERSON>O ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:56:27.3460-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:56:27.3460-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:56:27.6570-<PERSON><PERSON><PERSON> ProcUtils, <PERSON><PERSON><PERSON><PERSON> not completing the job, procId
2025-08-15 14:56:27.9628-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:56:27.9628-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:04.4863-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:57:04.7895-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:57:04.7895-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:05.1063-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:57:05.4117-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:57:05.4117-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:22.3513-INFO ProcUtils, KillProcess not completing the job, fileName
2025-08-15 14:57:22.3513-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:57:22.6523-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:57:22.6523-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:22.9614-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:57:23.2868-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:57:23.2868-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:41.8810-DEBUG DownloadService,不知道这样的主机。 (dash.pqjc.site:443)
2025-08-15 14:57:41.8810-DEBUG    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-08-15 14:57:41.8810-ERROR System.Net.Sockets.SocketException: 不知道这样的主机。
2025-08-15 14:57:51.8254-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:57:52.1295-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:57:52.1295-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:52.4442-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:57:52.7536-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:57:52.7536-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:57:53.9851-DEBUG DownloadService,不知道这样的主机。 (dash.pqjc.site:443)
2025-08-15 14:57:53.9851-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-08-15 14:57:53.9851-ERROR System.Net.Http.HttpRequestException: 不知道这样的主机。 (dash.pqjc.site:443)
2025-08-15 14:57:56.8414-DEBUG DownloadService,Arg_TimeoutException
2025-08-15 14:57:56.8414-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-08-15 14:58:05.7965-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_http_ssl_connection_failed)
2025-08-15 14:58:05.7965-DEBUG 
2025-08-15 14:58:05.7965-ERROR System.Net.Http.HttpRequestException: net_http_ssl_connection_failed
2025-08-15 14:58:11.8487-DEBUG DownloadService,Arg_TimeoutException
2025-08-15 14:58:11.8487-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-08-15 14:58:16.6838-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:58:16.9847-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:58:16.9847-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:58:17.2997-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:58:17.6068-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:58:17.6068-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:58:19.5636-DEBUG TaskScheduler_UnobservedTaskException,TaskExceptionHolder_UnhandledException (net_webstatus_Timeout)
2025-08-15 14:58:19.5636-DEBUG 
2025-08-15 14:58:19.5636-ERROR System.Net.WebException: net_webstatus_Timeout
2025-08-15 14:58:23.8934-DEBUG DownloadService,不知道这样的主机。 (dash.pqjc.site:443)
2025-08-15 14:58:23.8934-DEBUG    at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)
   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)
   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)
   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)
   at System.Net.Http.HttpClient.GetStringAsyncCore(HttpRequestMessage request, CancellationToken cancellationToken)
   at ServiceLib.Common.HttpClientHelper.GetAsync(HttpClient client, String url, CancellationToken token) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/HttpClientHelper.cs:line 59
   at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-08-15 14:58:23.8934-ERROR System.Net.Sockets.SocketException: 不知道这样的主机。
2025-08-15 14:58:35.9265-DEBUG DownloadService,不知道这样的主机。 (dash.pqjc.site:443)
2025-08-15 14:58:35.9265-DEBUG    at ServiceLib.Common.DownloaderHelper.<>c.<DownloadStringAsync>b__3_0(Object sender, AsyncCompletedEventArgs value) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 44
   at Downloader.AbstractDownloadService.OnDownloadFileCompleted(AsyncCompletedEventArgs e) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 441
   at Downloader.DownloadService.SendDownloadCompletionSignal(DownloadStatus state, Exception error) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 98
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 75
   at Downloader.DownloadService.StartDownload(Boolean forceBuildStorage) in /Users/<USER>/Sources/Downloader/src/Downloader/DownloadService.cs:line 80
   at Downloader.AbstractDownloadService.DownloadFileTaskAsync(String[] urls, CancellationToken cancellationToken) in /Users/<USER>/Sources/Downloader/src/Downloader/AbstractDownloadService.cs:line 214
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-08-15 14:58:35.9265-ERROR System.Net.Http.HttpRequestException: 不知道这样的主机。 (dash.pqjc.site:443)
2025-08-15 14:58:40.9322-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:58:41.2379-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:58:41.2379-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 14:58:41.5614-INFO ProcUtils, KillProcess not completing the job, procId
2025-08-15 14:58:41.8769-DEBUG ProcUtils,NoAssociatedProcess
2025-08-15 14:58:41.8769-DEBUG    at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.EnsureState(State state)
   at System.Diagnostics.Process.get_MainModule()
   at ServiceLib.Common.ProcUtils.ProcessKillByKeyInfo(Boolean review, Nullable`1 procId, String fileName, String processName) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/ProcUtils.cs:line 170
2025-08-15 21:51:45.7083-INFO MyAppExitAsync Begin
2025-08-15 21:51:46.0328-INFO MyAppExitAsync End
2025-08-15 21:51:46.0328-INFO OnExit
2025-08-15 23:20:16.3994-INFO v2rayN start up | v2rayN - V7.11.2 - X64 | C:\Users\<USER>\Desktop\VPN\ | C:\Users\<USER>\Desktop\VPN\v2rayN.exe | Microsoft Windows NT 10.0.22631.0
2025-08-15 23:20:16.8870-INFO Setup Scheduled Tasks
2025-08-15 23:20:59.5291-DEBUG DownloadService,Arg_TimeoutException
2025-08-15 23:20:59.5347-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
