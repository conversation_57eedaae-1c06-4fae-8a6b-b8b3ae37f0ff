2025-08-12 20:20:54.2676-INFO v2rayN start up | v2rayN - V7.11.2 - X64 | C:\Users\<USER>\Desktop\VPN\ | C:\Users\<USER>\Desktop\VPN\v2rayN.exe | Microsoft Windows NT 10.0.22631.0
2025-08-12 20:20:54.7016-<PERSON><PERSON>O Setup Scheduled Tasks
2025-08-12 20:21:13.3133-DEBUG DownloadService,Arg_TimeoutException
2025-08-12 20:21:13.3133-DEBUG    at ServiceLib.Services.DownloadService.DownloadStringAsync(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 167
2025-08-12 20:21:32.1607-DEBUG DownloadService,Arg_TimeoutException
2025-08-12 20:21:32.1607-DEBUG    at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 49
   at ServiceLib.Common.DownloaderHelper.DownloadStringAsync(IWebProxy webProxy, String url, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Common/DownloaderHelper.cs:line 54
   at ServiceLib.Services.DownloadService.DownloadStringViaDownloader(String url, Boolean blProxy, String userAgent, Int32 timeout) in /home/<USER>/work/v2rayN/v2rayN/v2rayN/ServiceLib/Services/DownloadService.cs:line 198
2025-08-12 23:04:26.5978-INFO Current_SessionEnding
2025-08-12 23:04:26.5978-INFO MyAppExitAsync Begin
